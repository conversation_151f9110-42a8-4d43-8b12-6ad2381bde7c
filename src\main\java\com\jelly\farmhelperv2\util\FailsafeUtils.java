package com.jelly.farmhelperv2.util;

import com.jelly.farmhelperv2.config.FarmHelperConfig;
import com.jelly.farmhelperv2.util.helper.KeyCodeConverter;
import com.sun.jna.platform.win32.User32;
import com.sun.jna.platform.win32.WinDef;
import com.sun.jna.platform.win32.WinUser;
import net.minecraft.client.Minecraft;
import org.apache.commons.lang3.SystemUtils;
import org.lwjgl.opengl.Display;

import javax.swing.*;
import java.awt.*;
import java.awt.event.KeyEvent;
import java.util.ArrayList;

public class FailsafeUtils {
    private static FailsafeUtils instance;

    public FailsafeUtils() {
        // SystemTray functionality removed for Android compatibility
    }

    public static FailsafeUtils getInstance() {
        if (instance == null) {
            instance = new FailsafeUtils();
        }
        return instance;
    }

    public static void bringWindowToFront() {
        if (SystemUtils.IS_OS_WINDOWS) {
            bringWindowToFrontUsingWinApi();
            System.out.println("Bringing window to front using WinApi.");
        } else {
            bringWindowToFrontUsingRobot();
            System.out.println("Bringing window to front using Robot.");
        }
    }

    public static void bringWindowToFrontUsingWinApi() {
        try {
            User32 user32 = User32.INSTANCE;
            WinDef.HWND hWnd = user32.FindWindow(null, Display.getTitle());
            if (hWnd == null) {
                System.out.println("Window not found.");
                bringWindowToFrontUsingRobot();
                return;
            }
            if (!user32.IsWindowVisible(hWnd)) {
                user32.ShowWindow(hWnd, WinUser.SW_RESTORE);
                System.out.println("Window is not visible, restoring.");
            }
            user32.ShowWindow(hWnd, WinUser.SW_SHOW);
            user32.SetForegroundWindow(hWnd);
            user32.SetFocus(hWnd);
        } catch (Exception e) {
            System.out.println("Failed to restore the game window.");
            e.printStackTrace();
            System.out.println("Trying to bring window to front using Robot instead.");
            bringWindowToFrontUsingRobot();
        }
    }

    public static void bringWindowToFrontUsingRobot() {
        SwingUtilities.invokeLater(() -> {
            int TAB_KEY = Minecraft.isRunningOnMac ? KeyEvent.VK_META : KeyEvent.VK_ALT;
            try {
                Robot robot = new Robot();
                int i = 0;
                while (!Display.isActive()) {
                    i++;
                    robot.keyPress(TAB_KEY);
                    for (int j = 0; j < i; j++) {
                        robot.keyPress(KeyEvent.VK_TAB);
                        robot.delay(100);
                        robot.keyRelease(KeyEvent.VK_TAB);
                    }
                    robot.keyRelease(TAB_KEY);
                    robot.delay(100);
                    if (i > 25) {
                        System.out.println("Failed to bring window to front.");
                        return;
                    }
                }
            } catch (AWTException e) {
                System.out.println("Failed to use Robot, got exception: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    public static void captureClip() {
        SwingUtilities.invokeLater(() -> {
            try {
                ArrayList<Integer> keys = FarmHelperConfig.captureClipKeybind.getKeyBinds();
                if (hasUndefinedKey(keys)) {
                    LogUtils.sendError("Failed to capture a clip! Keybind is either not set or invalid!");
                    return;
                }
                Robot robot = new Robot();
                keys.forEach(key -> {
                    robot.keyPress(KeyCodeConverter.convertToAwtKeyCode(key));
                });
                robot.delay(250);
                keys.forEach(key -> {
                    robot.keyRelease(KeyCodeConverter.convertToAwtKeyCode(key));
                });
            } catch (AWTException e) {
                System.out.println("Failed to use Robot, got exception: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private static boolean hasUndefinedKey(ArrayList<Integer> keys) {
        for (int key : keys)
            if (KeyCodeConverter.convertToAwtKeyCode(key) == KeyEvent.VK_UNDEFINED)
                return true;
        return false;
    }

    public void sendNotification(String text, Object type) {
        // SystemTray notifications removed for Android compatibility
        // Just log to console instead
        System.out.println("[Farm Helper Notification] " + text);
    }
}
