{"package": "com.jelly.farmhelperv2.mixin", "refmap": "mixins.${modid}.refmap.json", "minVersion": "0.7", "compatibilityLevel": "JAVA_8", "mixins": ["block.IBlockAccessor", "block.MixinBlockCocoa", "block.MixinBlockCrops", "block.MixinBlockMushroom", "block.MixinBlockNetherWart", "client.EntityPlayerAccessor", "client.MixinChunk", "client.MixinEntity", "client.MixinMouse", "client.MixinScoreboard", "fml.MixinFMLHandshakeMessage", "network.MixinNetworkManager", "pathfinder.MixinChunkProviderClient", "pathfinder.PathfinderAccessor"], "client": ["block.MixinBlock<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "client.EntityPlayerSPAccessor", "client.MinecraftAccessor", "client.MixinEntityPlayerSP", "client.MixinKeyBinding", "client.MixinMinecraft", "client.MixinPlayerControllerMP", "client.MixinSoundManager", "gui.AccessorGuiEditSign", "gui.IGuiPlayerTabOverlayAccessor", "gui<PERSON>", "gui.MixinGuiDisconnected", "gui.MixinGuiMainMenu", "gui.MixinGuiMultiplayer", "network.MixinNetHandlerPlayClient", "render.MixinActiveRenderInfo", "render.MixinEffectRenderer", "<PERSON>.MixinEntity<PERSON><PERSON>er", "render.MixinModelBiped", "render.MixinRenderManager"]}