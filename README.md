<p align="center">
  <a href="https://github.com/JellyLabScripts/FarmHelper/graphs/contributors" alt="Contributors">
    <img src="https://img.shields.io/github/contributors/JellyLabScripts/FarmHelper?color=blue" />
  </a>
  <a href="https://github.com/JellyLabScripts/FarmHelper/releases" target="_blank">
    <img alt="release" src="https://img.shields.io/github/v/release/JellyLabScripts/FarmHelper?color=green" />
  </a>
  <a href="https://github.com/JellyLabScripts/FarmHelper/releases" target="_blank">
    <img alt="downloads" src="https://img.shields.io/github/downloads/JellyLabScripts/FarmHelper/total?color=purple" />
  </a>
  <a href="https://discord.gg/6mSHC2Xd9y" target="_blank">
    <img alt="discord" src="https://img.shields.io/discord/450878205294018560?color=orange&label=discord" />
  </a>
</p>

<br />
<div align="center">
  <a href="https://github.com/JellyLabScripts/FarmHelper">
    <img src="images/logo.png" alt="Logo" width="80" height="80">
  </a>

<h3 align="center">FarmHelper V2</h3>
  <p align="center">
    <a href="https://discord.gg/6mSHC2Xd9y">Report Bug</a>
    ·
    <a href="https://discord.gg/6mSHC2Xd9y">Request Feature</a>
  </p>
</div>

## About The Project

Imagine not having to stare at your computer for 12 hours a day just to press the A and D keys. Imagine, being able to skip all the repetitive processes of farming and do other things simultaneously. Farm Helper is a highly customizable skyblock quality of life (QOL) mod that automates boring farming processes, freeing up your time for other activities.

Revolutionizing the farming meta of Hypixel skyblock, Farm Helper has already been downloaded over 30,000 times. Start your QOL journey by joining the Jellylab community!

## Contributing

Contributions are what make the open source community such an amazing place to learn, inspire, and create. Any contributions you make are **greatly appreciated**.

If you have a suggestion that would make this better, please fork the repo and create a pull request.
Don't forget to give the project a star! Thanks again!

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

Distributed under a custom license. See `LICENSE` for more information.

[contributors-shield]: https://img.shields.io/github/contributors/JellyLabScripts/FarmHelper.svg?style=for-the-badge
[contributors-url]: https://github.com/JellyLabScripts/FarmHelper/graphs/contributors
[forks-shield]: https://img.shields.io/github/forks/JellyLabScripts/FarmHelper.svg?style=for-the-badge
[forks-url]: https://github.com/JellyLabScripts/FarmHelper/network/members
[stars-shield]: https://img.shields.io/github/stars/JellyLabScripts/FarmHelper.svg?style=for-the-badge
[stars-url]: https://github.com/JellyLabScripts/FarmHelper/stargazers
[issues-shield]: https://img.shields.io/github/issues/JellyLabScripts/FarmHelper.svg?style=for-the-badge
[issues-url]: https://github.com/JellyLabScripts/FarmHelper/issues
[license-shield]: https://img.shields.io/github/license/JellyLabScripts/FarmHelper.svg?style=for-the-badge
[license-url]: https://github.com/JellyLabScripts/FarmHelper/blob/master/LICENSE
[downloads-shield]: https://img.shields.io/github/downloads/JellyLabScripts/FarmHelper/total.svg?style=for-the-badge
